<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Switch } from '$lib/components/ui/switch';
  import { Label } from '$lib/components/ui/label';
  import { Badge } from '$lib/components/ui/badge';
  import { Input } from '$lib/components/ui/input';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';
  import {
    Bell,
    Briefcase,
    MessageSquare,
    AlertTriangle,
    Info,
    CheckCircle,
    RefreshCw,
    Trash2,
    ChevronLeft,
    ChevronRight,
    X,
    Search,
    Settings,
  } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { notifications } from '$lib/stores/notification';
  import * as ScrollArea from '$lib/components/ui/scroll-area';
  import * as Select from '$lib/components/ui/select/index.js';

  const { data } = $props();

  // Destructure data reactively
  let serverNotifications = $state(data.notifications);
  let pagination = $state(data.pagination);
  let filters = $state(data.filters);
  let unreadCount = $state(data.unreadCount);

  // State
  let loading = $state(false);
  let selectedType = $state(filters.type || 'all');
  let includeRead = $state(filters.includeRead || false);
  let searchQuery = $state('');
  let showFilters = $state(false);

  // Computed filtered notifications
  let filteredNotifications = $derived.by(() => {
    if (!searchQuery.trim()) {
      return serverNotifications;
    }

    const query = searchQuery.toLowerCase().trim();
    return serverNotifications.filter(
      (notification: any) =>
        notification.title.toLowerCase().includes(query) ||
        notification.message.toLowerCase().includes(query) ||
        (notification.type && notification.type.toLowerCase().includes(query))
    );
  });

  // Check if any filters are active
  let hasActiveFilters = $derived.by(() => {
    return selectedType !== 'all' || includeRead || searchQuery.trim() !== '';
  });

  // Get icon for notification type
  function getNotificationIcon(type: string | undefined) {
    switch (type) {
      case 'job':
        return Briefcase;
      case 'application':
        return CheckCircle;
      case 'interview':
        return MessageSquare;
      case 'error':
        return AlertTriangle;
      case 'success':
        return CheckCircle;
      case 'message':
        return MessageSquare;
      case 'info':
      case 'system':
      default:
        return Info;
    }
  }

  // Format date
  function formatDate(dateString: string) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return 'just now';
    } else if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  // Mark notification as read
  async function markAsRead(id: string) {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'markAsRead',
          id,
        }),
      });

      if (response.ok) {
        // Update local state
        serverNotifications = serverNotifications.map((notif: any) => {
          if (notif.id === id) {
            return { ...notif, read: true };
          }
          return notif;
        });

        // Update unread count
        if (unreadCount > 0) {
          unreadCount--;
        }
      } else {
        toast.error('Failed to mark notification as read');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  }

  // Delete notification
  async function deleteNotification(id: string) {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'delete',
          id,
        }),
      });

      if (response.ok) {
        // Update local state
        const deletedNotif = serverNotifications.find((n: any) => n.id === id);
        serverNotifications = serverNotifications.filter((notif: any) => notif.id !== id);

        // Update unread count if the deleted notification was unread
        if (deletedNotif && !deletedNotif.read && unreadCount > 0) {
          unreadCount--;
        }

        toast.success('Notification deleted');
      } else {
        toast.error('Failed to delete notification');
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('Failed to delete notification');
    }
  }

  // Mark all as read
  async function markAllAsRead() {
    loading = true;

    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'POST',
      });

      if (response.ok) {
        // Update local state
        serverNotifications = serverNotifications.map((notif: any) => ({
          ...notif,
          read: true,
        }));

        // Update unread count
        unreadCount = 0;

        toast.success('All notifications marked as read');
      } else {
        toast.error('Failed to mark all notifications as read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    } finally {
      loading = false;
    }
  }

  // Apply filters
  function applyFilters() {
    const searchParams = new URLSearchParams();
    searchParams.set('page', '1');

    if (selectedType !== 'all') {
      searchParams.set('type', selectedType);
    }

    if (includeRead) {
      searchParams.set('includeRead', 'true');
    }

    goto(`?${searchParams.toString()}`);
  }

  // Clear all filters
  function clearFilters() {
    selectedType = 'all';
    includeRead = false;
    searchQuery = '';
    applyFilters();
  }

  // Go to page
  function goToPage(pageNum: number) {
    const url = new URL(window.location.href);
    url.searchParams.set('page', pageNum.toString());
    goto(url.search);
  }

  // Sync client-side notifications with server-side data using reactive effect
  $effect(() => {
    // Update client-side notification store with server data
    notifications.set(
      serverNotifications.map((notif: any) => ({
        ...notif,
        timestamp: new Date(notif.createdAt),
      }))
    );
  });
</script>

<SEO
  title="Notifications - Hirli"
  description="Manage your notifications and stay up-to-date with the latest news and updates from Hirli." />

<div class="flex flex-col">
  <div class="border-border flex items-center justify-between border-b p-4">
    <h1 class="text-2xl font-bold">Notifications</h1>

    <div class="flex items-center gap-3">
      {#if unreadCount > 0}
        <Button variant="outline" size="sm" onclick={markAllAsRead} disabled={loading}>
          {#if loading}
            <RefreshCw class="mr-2 h-4 w-4 animate-spin" />
          {:else}
            <CheckCircle class="mr-2 h-4 w-4" />
          {/if}
          Mark All as Read
        </Button>
      {/if}
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="border-border border-b">
    <!-- Search Bar -->
    <div class="flex items-center gap-3 px-4 py-3">
      <div class="relative flex-1">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search notifications..."
          bind:value={searchQuery}
          class="pl-10" />
      </div>

      <Button
        variant="outline"
        size="sm"
        onclick={() => (showFilters = !showFilters)}
        class="gap-2">
        <Settings class="h-4 w-4" />
        Filters
        {#if hasActiveFilters}
          <Badge variant="secondary" class="ml-1 h-5 w-5 rounded-full p-0 text-xs">
            {(selectedType !== 'all' ? 1 : 0) + (includeRead ? 1 : 0)}
          </Badge>
        {/if}
      </Button>

      {#if hasActiveFilters}
        <Button variant="ghost" size="sm" onclick={clearFilters} class="gap-2">
          <X class="h-4 w-4" />
          Clear
        </Button>
      {/if}
    </div>

    <!-- Filter Controls -->
    {#if showFilters}
      <div class="border-border flex flex-wrap items-center gap-4 border-t px-4 py-3">
        <div class="flex items-center gap-2">
          <Label for="type-select" class="text-sm font-medium">Type:</Label>
          <Select.Root
            type="single"
            value={selectedType}
            onValueChange={(value) => {
              selectedType = value;
              applyFilters();
            }}>
            <Select.Trigger class="w-40" id="type-select">
              <Select.Value placeholder={selectedType === 'all' ? 'All Types' : selectedType} />
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Item value="all">All Types</Select.Item>
              <Select.Item value="system">System</Select.Item>
              <Select.Item value="job">Job</Select.Item>
              <Select.Item value="application">Application</Select.Item>
              <Select.Item value="interview">Interview</Select.Item>
              <Select.Item value="message">Message</Select.Item>
              <Select.Item value="success">Success</Select.Item>
              <Select.Item value="error">Error</Select.Item>
              <Select.Item value="warning">Warning</Select.Item>
              <Select.Item value="info">Info</Select.Item>
            </Select.Content>
          </Select.Root>
        </div>

        <div class="flex items-center space-x-2">
          <Switch
            id="includeRead"
            checked={includeRead}
            onCheckedChange={(checked) => {
              includeRead = checked;
              applyFilters();
            }} />
          <Label for="includeRead" class="text-sm">Include Read</Label>
        </div>
      </div>
    {/if}
  </div>

  <!-- Results Count -->
  {#if searchQuery.trim() || hasActiveFilters}
    <div class="border-border border-b px-4 py-2">
      <p class="text-muted-foreground text-sm">
        Showing {filteredNotifications.length} of {serverNotifications.length} notifications
        {#if searchQuery.trim()}
          for "{searchQuery}"
        {/if}
      </p>
    </div>
  {/if}

  {#if filteredNotifications.length === 0}
    <Card.Root>
      <Card.Content class="flex flex-col items-center justify-center py-12">
        <Bell class="text-muted-foreground mb-4 h-12 w-12 opacity-20" />
        <h2 class="mb-2 text-xl font-semibold">
          {searchQuery.trim() ? 'No matching notifications' : 'No notifications'}
        </h2>
        <p class="text-muted-foreground">
          {searchQuery.trim()
            ? 'Try adjusting your search or filters.'
            : "You don't have any notifications yet."}
        </p>
        {#if hasActiveFilters}
          <Button variant="outline" size="sm" onclick={clearFilters} class="mt-3">
            Clear filters
          </Button>
        {/if}
      </Card.Content>
    </Card.Root>
  {:else}
    <ScrollArea.Root class="h-[calc(100vh-240px)] overflow-hidden p-4">
      <div class="space-y-4">
        {#each filteredNotifications as notification}
          {@const IconComponent = getNotificationIcon(notification.type)}
          <Card.Root
            class={notification.read ? 'opacity-80 transition-opacity hover:opacity-100' : ''}>
            <Card.Content class="p-4">
              <div class="flex items-start gap-4">
                <div class="bg-primary/10 flex-shrink-0 rounded-full p-2">
                  <IconComponent class="text-primary h-5 w-5" />
                </div>

                <div class="flex-1">
                  <div class="flex flex-wrap items-center gap-2">
                    <h3 class="text-base font-medium">{notification.title}</h3>
                    {#if !notification.read}
                      <Badge variant="default" class="h-1.5 w-1.5 rounded-full p-0" />
                    {/if}
                    {#if notification.global}
                      <Badge variant="outline" class="text-xs">Global</Badge>
                    {/if}
                    <Badge variant="outline" class="text-xs capitalize"
                      >{notification.type || 'info'}</Badge>
                  </div>

                  <p class="text-muted-foreground mt-1 text-sm">{notification.message}</p>

                  {#if notification.url}
                    <a
                      href={notification.url}
                      class="text-primary hover:text-primary/80 mt-2 inline-block text-sm"
                      onclick={() => markAsRead(notification.id)}>
                      View Details
                    </a>
                  {/if}

                  <p class="text-muted-foreground mt-2 text-xs">
                    {formatDate(notification.createdAt)}
                  </p>
                </div>

                <div class="flex items-center gap-2">
                  {#if !notification.read}
                    <Button
                      variant="ghost"
                      size="sm"
                      class="h-8 w-8 p-0"
                      onclick={() => markAsRead(notification.id)}>
                      <CheckCircle class="h-4 w-4" />
                    </Button>
                  {/if}

                  <Button
                    variant="ghost"
                    size="sm"
                    class="text-destructive hover:text-destructive/80 h-8 w-8 p-0"
                    onclick={() => deleteNotification(notification.id)}>
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card.Content>
          </Card.Root>
        {/each}
      </div>
    </ScrollArea.Root>
  {/if}

  <!-- Pagination -->
  {#if pagination.totalPages > 1 && !searchQuery.trim()}
    <div class="mt-6 flex items-center justify-center gap-2">
      <Button
        variant="outline"
        size="sm"
        disabled={pagination.page === 1}
        onclick={() => goToPage(pagination.page - 1)}
        class="gap-1">
        <ChevronLeft class="h-4 w-4" />
        Previous
      </Button>

      <div class="flex items-center gap-1">
        {#each Array(pagination.totalPages) as _, i}
          {#if pagination.totalPages <= 7 || i + 1 === 1 || i + 1 === pagination.totalPages || (i + 1 >= pagination.page - 1 && i + 1 <= pagination.page + 1)}
            <Button
              variant={pagination.page === i + 1 ? 'default' : 'outline'}
              size="sm"
              onclick={() => goToPage(i + 1)}
              class="h-8 w-8 p-0">
              {i + 1}
            </Button>
          {:else if (i + 1 === 2 && pagination.page > 3) || (i + 1 === pagination.totalPages - 1 && pagination.page < pagination.totalPages - 2)}
            <div class="flex h-8 w-8 items-center justify-center">...</div>
          {/if}
        {/each}
      </div>

      <Button
        variant="outline"
        size="sm"
        disabled={pagination.page === pagination.totalPages}
        onclick={() => goToPage(pagination.page + 1)}
        class="gap-1">
        Next
        <ChevronRight class="h-4 w-4" />
      </Button>
    </div>
  {/if}
</div>
